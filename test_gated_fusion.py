#!/usr/bin/env python3
"""
测试门控融合机制的示例脚本
演示如何使用GatedFusion类融合混合注意力与CBAM
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from models.feature_fusion import GatedFusion, HybridAttention, Decision_Fusion
from models.temporal_spatial_stream import CBAM

def test_gated_fusion_4d():
    """测试4D输入的门控融合（适用于卷积特征图）"""
    print("=== 测试4D输入的门控融合 ===")
    
    # 创建模拟的4D特征图输入 (batch_size, channels, height, width)
    batch_size, channels, height, width = 2, 64, 8, 10
    x = torch.randn(batch_size, channels, height, width)
    
    # 创建门控融合模块
    gated_fusion = GatedFusion(channels=channels, ratio=8, kernel_size=7)
    
    # 前向传播
    output = gated_fusion(x)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"门控权重: {gated_fusion.gate.item():.4f}")
    
    # 验证输出形状是否正确
    assert output.shape == x.shape, f"输出形状不匹配: 期望 {x.shape}, 实际 {output.shape}"
    print("✅ 4D输入测试通过")
    
    return gated_fusion, x, output

def test_gated_fusion_2d():
    """测试2D输入的门控融合（适用于特征向量）"""
    print("\n=== 测试2D输入的门控融合 ===")
    
    # 创建模拟的2D特征向量输入 (batch_size, feature_dim)
    batch_size, feature_dim = 4, 128
    x = torch.randn(batch_size, 1, feature_dim)  # 添加序列维度
    
    # 创建门控融合模块
    gated_fusion = GatedFusion(channels=None, dim=feature_dim, num_heads=4)
    
    # 前向传播
    output = gated_fusion(x)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"门控权重: {gated_fusion.gate.item():.4f}")
    
    # 验证输出形状是否正确
    assert output.shape == x.shape, f"输出形状不匹配: 期望 {x.shape}, 实际 {output.shape}"
    print("✅ 2D输入测试通过")
    
    return gated_fusion, x, output

def test_decision_fusion_with_gated():
    """测试集成门控融合的Decision_Fusion模型"""
    print("\n=== 测试Decision_Fusion with 门控融合 ===")
    
    # 创建模拟输入
    batch_size = 2
    TS_input = torch.randn(batch_size, 1, 4, 280)  # TS流输入
    TF_input = torch.randn(batch_size, 4, 20, 280)  # TF流输入
    
    # 创建使用门控融合的模型
    model_gated = Decision_Fusion(n_classes=4, proj_dim=128, num_heads=4, use_gated_fusion=True)
    
    # 创建不使用门控融合的模型（对比）
    model_original = Decision_Fusion(n_classes=4, proj_dim=128, num_heads=4, use_gated_fusion=False)
    
    # 前向传播
    with torch.no_grad():
        output_gated = model_gated(TS_input, TF_input)
        output_original = model_original(TS_input, TF_input)
    
    print(f"TS输入形状: {TS_input.shape}")
    print(f"TF输入形状: {TF_input.shape}")
    print(f"门控融合输出形状: {output_gated.shape}")
    print(f"原始模型输出形状: {output_original.shape}")
    
    # 检查门控权重
    print(f"TS流门控权重: {model_gated.gated_fusion_TS.gate.item():.4f}")
    print(f"TF流门控权重: {model_gated.gated_fusion_TF.gate.item():.4f}")
    
    # 验证输出形状
    assert output_gated.shape == (batch_size, 4), f"门控融合输出形状错误: {output_gated.shape}"
    assert output_original.shape == (batch_size, 4), f"原始模型输出形状错误: {output_original.shape}"
    
    print("✅ Decision_Fusion测试通过")
    
    return model_gated, model_original, output_gated, output_original

def visualize_gate_weights():
    """可视化门控权重的学习过程"""
    print("\n=== 可视化门控权重学习过程 ===")
    
    # 创建简单的门控融合模块
    gated_fusion = GatedFusion(channels=32, ratio=8)
    
    # 模拟训练过程中门控权重的变化
    gate_weights = []
    losses = []
    
    # 创建模拟数据和目标
    x = torch.randn(8, 32, 16, 16)
    target = torch.randn(8, 32, 16, 16)
    
    # 简单的优化器
    optimizer = torch.optim.Adam([gated_fusion.gate], lr=0.01)
    criterion = nn.MSELoss()
    
    # 模拟训练
    for epoch in range(100):
        optimizer.zero_grad()
        
        output = gated_fusion(x)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()
        
        gate_weights.append(gated_fusion.gate.item())
        losses.append(loss.item())
        
        if epoch % 20 == 0:
            print(f"Epoch {epoch}: Loss = {loss.item():.4f}, Gate = {gated_fusion.gate.item():.4f}")
    
    # 绘制门控权重变化
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(gate_weights)
    plt.title('门控权重变化')
    plt.xlabel('训练步数')
    plt.ylabel('门控权重值')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(losses)
    plt.title('损失函数变化')
    plt.xlabel('训练步数')
    plt.ylabel('损失值')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('gated_fusion_training.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"最终门控权重: {gated_fusion.gate.item():.4f}")
    print("✅ 门控权重可视化完成，图片保存为 gated_fusion_training.png")

def compare_attention_mechanisms():
    """比较不同注意力机制的效果"""
    print("\n=== 比较注意力机制效果 ===")
    
    # 创建测试数据
    batch_size, channels, height, width = 4, 64, 8, 10
    x = torch.randn(batch_size, channels, height, width)
    
    # 创建不同的注意力模块
    cbam = CBAM(channels)
    gated_fusion = GatedFusion(channels=channels)
    
    # 前向传播
    with torch.no_grad():
        cbam_out = cbam(x)
        gated_out = gated_fusion(x)
    
    # 计算注意力强度（使用方差作为指标）
    original_var = torch.var(x).item()
    cbam_var = torch.var(cbam_out).item()
    gated_var = torch.var(gated_out).item()
    
    print(f"原始特征方差: {original_var:.4f}")
    print(f"CBAM输出方差: {cbam_var:.4f}")
    print(f"门控融合输出方差: {gated_var:.4f}")
    
    # 计算相对变化
    cbam_change = (cbam_var - original_var) / original_var * 100
    gated_change = (gated_var - original_var) / original_var * 100
    
    print(f"CBAM相对变化: {cbam_change:.2f}%")
    print(f"门控融合相对变化: {gated_change:.2f}%")
    
    print("✅ 注意力机制比较完成")

def main():
    """主函数：运行所有测试"""
    print("🚀 开始测试门控融合机制")
    
    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # 运行各项测试
        test_gated_fusion_4d()
        test_gated_fusion_2d()
        test_decision_fusion_with_gated()
        visualize_gate_weights()
        compare_attention_mechanisms()
        
        print("\n🎉 所有测试完成！门控融合机制工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise

if __name__ == "__main__":
    main()
