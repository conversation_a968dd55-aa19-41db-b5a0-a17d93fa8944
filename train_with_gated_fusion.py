#!/usr/bin/env python3
"""
使用门控融合机制的训练示例脚本
演示如何在实际训练中集成门控融合机制
"""

import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import matplotlib.pyplot as plt
from models.feature_fusion import Decision_Fusion

def create_synthetic_data(num_samples=1000, batch_size=32):
    """创建合成数据用于演示"""
    print("创建合成数据...")
    
    # 生成TS流数据 (batch_size, 1, 4, 280)
    ts_data = torch.randn(num_samples, 1, 4, 280)
    
    # 生成TF流数据 (batch_size, 4, 20, 280)  
    tf_data = torch.randn(num_samples, 4, 20, 280)
    
    # 生成随机标签 (4个类别)
    labels = torch.randint(0, 4, (num_samples,))
    
    # 创建数据集和数据加载器
    dataset = TensorDataset(ts_data, tf_data, labels)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    print(f"数据集大小: {num_samples}")
    print(f"批次大小: {batch_size}")
    print(f"TS数据形状: {ts_data[0].shape}")
    print(f"TF数据形状: {tf_data[0].shape}")
    
    return dataloader

def train_model_comparison(train_loader, num_epochs=20):
    """比较使用门控融合和不使用门控融合的模型训练效果"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建两个模型：一个使用门控融合，一个不使用
    model_gated = Decision_Fusion(n_classes=4, use_gated_fusion=True).to(device)
    model_original = Decision_Fusion(n_classes=4, use_gated_fusion=False).to(device)
    
    # 优化器和损失函数
    optimizer_gated = optim.Adam(model_gated.parameters(), lr=0.001)
    optimizer_original = optim.Adam(model_original.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    # 记录训练过程
    history = {
        'gated_loss': [],
        'original_loss': [],
        'gated_acc': [],
        'original_acc': [],
        'ts_gate_weights': [],
        'tf_gate_weights': []
    }
    
    print("\n开始训练...")
    print("=" * 60)
    
    for epoch in range(num_epochs):
        # 训练模式
        model_gated.train()
        model_original.train()
        
        epoch_loss_gated = 0.0
        epoch_loss_original = 0.0
        epoch_acc_gated = 0.0
        epoch_acc_original = 0.0
        num_batches = 0
        
        for batch_idx, (ts_data, tf_data, labels) in enumerate(train_loader):
            ts_data, tf_data, labels = ts_data.to(device), tf_data.to(device), labels.to(device)
            
            # 训练门控融合模型
            optimizer_gated.zero_grad()
            outputs_gated = model_gated(ts_data, tf_data)
            loss_gated = criterion(outputs_gated, labels)
            loss_gated.backward()
            optimizer_gated.step()
            
            # 训练原始模型
            optimizer_original.zero_grad()
            outputs_original = model_original(ts_data, tf_data)
            loss_original = criterion(outputs_original, labels)
            loss_original.backward()
            optimizer_original.step()
            
            # 计算准确率
            _, pred_gated = torch.max(outputs_gated, 1)
            _, pred_original = torch.max(outputs_original, 1)
            acc_gated = (pred_gated == labels).float().mean()
            acc_original = (pred_original == labels).float().mean()
            
            # 累积统计
            epoch_loss_gated += loss_gated.item()
            epoch_loss_original += loss_original.item()
            epoch_acc_gated += acc_gated.item()
            epoch_acc_original += acc_original.item()
            num_batches += 1
        
        # 计算平均值
        avg_loss_gated = epoch_loss_gated / num_batches
        avg_loss_original = epoch_loss_original / num_batches
        avg_acc_gated = epoch_acc_gated / num_batches
        avg_acc_original = epoch_acc_original / num_batches
        
        # 记录门控权重
        ts_gate = model_gated.gated_fusion_TS.gate.item()
        tf_gate = model_gated.gated_fusion_TF.gate.item()
        
        # 保存历史记录
        history['gated_loss'].append(avg_loss_gated)
        history['original_loss'].append(avg_loss_original)
        history['gated_acc'].append(avg_acc_gated)
        history['original_acc'].append(avg_acc_original)
        history['ts_gate_weights'].append(ts_gate)
        history['tf_gate_weights'].append(tf_gate)
        
        # 打印进度
        if epoch % 5 == 0 or epoch == num_epochs - 1:
            print(f"Epoch {epoch+1:2d}/{num_epochs}")
            print(f"  门控融合 - Loss: {avg_loss_gated:.4f}, Acc: {avg_acc_gated:.4f}")
            print(f"  原始模型 - Loss: {avg_loss_original:.4f}, Acc: {avg_acc_original:.4f}")
            print(f"  门控权重 - TS: {ts_gate:.4f}, TF: {tf_gate:.4f}")
            print("-" * 50)
    
    return history, model_gated, model_original

def visualize_training_results(history):
    """可视化训练结果"""
    print("\n生成训练结果可视化...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    epochs = range(1, len(history['gated_loss']) + 1)
    
    # 损失对比
    axes[0, 0].plot(epochs, history['gated_loss'], 'b-', label='门控融合', linewidth=2)
    axes[0, 0].plot(epochs, history['original_loss'], 'r-', label='原始模型', linewidth=2)
    axes[0, 0].set_title('Training Loss Comparison', fontsize=14)
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 准确率对比
    axes[0, 1].plot(epochs, history['gated_acc'], 'b-', label='门控融合', linewidth=2)
    axes[0, 1].plot(epochs, history['original_acc'], 'r-', label='原始模型', linewidth=2)
    axes[0, 1].set_title('Training Accuracy Comparison', fontsize=14)
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # TS流门控权重变化
    axes[1, 0].plot(epochs, history['ts_gate_weights'], 'g-', linewidth=2)
    axes[1, 0].set_title('TS Stream Gate Weight Evolution', fontsize=14)
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Gate Weight')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].axhline(y=0.5, color='k', linestyle='--', alpha=0.5, label='Initial Value')
    axes[1, 0].legend()
    
    # TF流门控权重变化
    axes[1, 1].plot(epochs, history['tf_gate_weights'], 'orange', linewidth=2)
    axes[1, 1].set_title('TF Stream Gate Weight Evolution', fontsize=14)
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Gate Weight')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].axhline(y=0.5, color='k', linestyle='--', alpha=0.5, label='Initial Value')
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('gated_fusion_training_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("训练结果可视化已保存为: gated_fusion_training_comparison.png")

def analyze_gate_weights(history):
    """分析门控权重的学习模式"""
    print("\n=== 门控权重分析 ===")
    
    ts_weights = history['ts_gate_weights']
    tf_weights = history['tf_gate_weights']
    
    print(f"TS流门控权重:")
    print(f"  初始值: {ts_weights[0]:.4f}")
    print(f"  最终值: {ts_weights[-1]:.4f}")
    print(f"  变化量: {ts_weights[-1] - ts_weights[0]:.4f}")
    print(f"  标准差: {np.std(ts_weights):.4f}")
    
    print(f"\nTF流门控权重:")
    print(f"  初始值: {tf_weights[0]:.4f}")
    print(f"  最终值: {tf_weights[-1]:.4f}")
    print(f"  变化量: {tf_weights[-1] - tf_weights[0]:.4f}")
    print(f"  标准差: {np.std(tf_weights):.4f}")
    
    # 分析权重趋势
    if ts_weights[-1] > 0.5:
        print(f"\nTS流更偏向混合注意力 (权重 > 0.5)")
    else:
        print(f"\nTS流更偏向CBAM (权重 < 0.5)")
        
    if tf_weights[-1] > 0.5:
        print(f"TF流更偏向混合注意力 (权重 > 0.5)")
    else:
        print(f"TF流更偏向CBAM (权重 < 0.5)")

def save_training_config(history, model_gated):
    """保存训练配置和结果"""
    config = {
        'model_type': 'Decision_Fusion_with_GatedFusion',
        'use_gated_fusion': True,
        'final_ts_gate_weight': history['ts_gate_weights'][-1],
        'final_tf_gate_weight': history['tf_gate_weights'][-1],
        'final_loss': history['gated_loss'][-1],
        'final_accuracy': history['gated_acc'][-1],
        'training_epochs': len(history['gated_loss'])
    }
    
    with open('gated_fusion_training_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n训练配置已保存为: gated_fusion_training_config.json")

def main():
    """主函数"""
    print("🚀 开始门控融合机制训练演示")
    print("=" * 60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # 创建数据
        train_loader = create_synthetic_data(num_samples=800, batch_size=32)
        
        # 训练模型
        history, model_gated, model_original = train_model_comparison(
            train_loader, num_epochs=20
        )
        
        # 可视化结果
        visualize_training_results(history)
        
        # 分析门控权重
        analyze_gate_weights(history)
        
        # 保存配置
        save_training_config(history, model_gated)
        
        print("\n🎉 门控融合训练演示完成！")
        print("生成的文件:")
        print("  - gated_fusion_training_comparison.png (训练过程可视化)")
        print("  - gated_fusion_training_config.json (训练配置)")
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        raise

if __name__ == "__main__":
    main()
