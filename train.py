import os  # 导入 os 模块，用于处理文件路径和目录
import time  # 导入 time 模块，用于计算时间
import torch  # 导入 PyTorch 库，用于深度学习任务
import numpy as np  # 导入 numpy 库，用于数值计算
from tqdm import tqdm  # 导入 tqdm 库，用于显示进度条
import pandas as pd  # 导入 pandas 库，用于数据处理
from torch import nn  # 导入 PyTorch 的神经网络模块
from torch.optim.lr_scheduler import MultiStepLR  # 导入学习率调度器
from torch.cuda.amp import autocast, GradScaler

from data.data_processing import batch_cwt  # 从 data_processing 模块中导入 batch_cwt 函数，用于计算连续小波变换 (CWT)
from data.data_loader import get_source_data  # 从 data_loader 模块中导入 get_source_data 函数，用于加载数据
from data.data_augmentation import interaug  # 从 data_augmentation 模块中导入 interaug 函数，用于数据增强
from utilities.plotting import plot_metrics, log_metrics  # 从 plotting 模块中导入绘图和日志记录函数
from utilities.utils import EarlyStopping  # 从 utils 模块中导入 EarlyStopping 类，用于早停机制
from models.feature_fusion import Decision_Fusion  # 从 feature_fusion 模块中导入 Decision_Fusion 模型
from test import evaluate  # 从 test 模块中导入 evaluate 函数，用于模型评估

gpus = [0]  # 指定使用的 GPU 设备


def train_one_epoch(model, dataloader, optimizer, scheduler, criterion_cls, 
                    original_data, original_label, batch_size, signal_length, 
                    device, behavioral_features, freq_min, freq_max, tensor_height, 
                    sampling_frequency):
    """
    训练模型一个 epoch。

    参数:
        model (nn.Module): 神经网络模型。
        dataloader (DataLoader): 训练数据的 DataLoader。
        optimizer (torch.optim.Optimizer): 模型参数的优化器。
        scheduler (torch.optim.lr_scheduler._LRScheduler): 学习率调度器。
        criterion_cls (nn.Module): 损失函数。
        original_data (np.array): 用于数据增强的原始训练数据。
        original_label (np.array): 用于数据增强的原始训练标签。
        batch_size (int): 批次大小。
        signal_length (int): 信号的长度。
        device (torch.device): 运行训练的设备（CPU 或 GPU）。
        behavioral_features (list of str): 使用的行为特征列表。
        freq_min (float): CWT 的最小频率。
        freq_max (float): CWT 的最大频率。
        tensor_height (int): CWT 的离散频率数量。
        sampling_frequency (int): 信号的采样频率。

    返回:
        tuple: 包含 epoch 损失、训练准确率、epoch 持续时间和当前学习率的元组。
    """
    
    # 记录 epoch 的开始时间
    start_time = time.time()
    
    # 初始化损失和准确率的计数器
    total_loss = 0
    num_batches = 0
    total_correct = 0
    total_samples = 0

    model.train()  # 将模型设置为训练模式

    print("original_data.shape:",original_data.shape)
    print("original_label.shape:",original_label.shape)

    # 添加混合精度训练的scaler
    scaler = GradScaler()

    for _, (train_signal_data, train_label) in enumerate(tqdm(dataloader, desc="Processing")):
        # 动态数据增强并与现有批次拼接
        # if torch.isnan(train_signal_data).any() or torch.isinf(train_signal_data).any():
        #     print("**NaN or Inf detected in train_signal_data.**")
        aug_signal_data, aug_label = interaug(
            original_data, original_label, batch_size, 
            signal_length, device, num_behavioral_features=len(behavioral_features))
        # if torch.isnan(train_signal_data).any() or torch.isinf(train_signal_data).any():
        #     print("$$NaN or Inf detected in train_signal_data.$$")
        
        train_signal_data = torch.cat((train_signal_data, aug_signal_data))
        train_label = torch.cat((train_label, aug_label))

        # 使用autocast进行混合精度计算
        with autocast():
            # 对数据应用连续小波变换 (CWT)
            frequencies = np.linspace(freq_min, freq_max, tensor_height)
            train_cwt_data = batch_cwt(train_signal_data, frequencies, sampling_frequency=sampling_frequency)

            # 前向传播，计算模型输出
            outputs = model(train_signal_data, train_cwt_data)
            loss = criterion_cls(outputs, train_label)  # 计算损失
        
        # 使用scaler进行反向传播和优化
        optimizer.zero_grad()
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        
        total_loss += loss.item()
        num_batches += 1

        # 计算准确率
        _, predicted = torch.max(outputs.data, 1)
        total_correct += (predicted == train_label).sum().item()
        total_samples += train_label.size(0)

    print("train_signal_data.shape(增强后):",train_signal_data.shape)
    print("train_label.shape(增强后):",train_label.shape)

    # 更新学习率调度器并计算 epoch 的指标
    scheduler.step()
    current_lr = scheduler.get_last_lr()[0]  # 获取当前学习率
    epoch_loss = total_loss / num_batches  # 计算平均损失
    train_acc = total_correct / total_samples  # 计算训练准确率

    # 计算 epoch 的持续时间
    end_time = time.time()
    duration = end_time - start_time

    return epoch_loss, train_acc, duration, current_lr  # 返回 epoch 损失、训练准确率、持续时间和当前学习率


def train(n_classes, batch_size, b1, b2, n_epochs, lr, behavioral_features,
          train_folder_path, test_folder_path, label_file, milestones, gamma,
          patience, sampling_frequency, weight_decay, freq_min, freq_max, tensor_height,
          use_gated_fusion=True, proj_dim=128, num_heads=4):
    """
    训练模型并在测试集上评估。

    参数:
        n_classes (int): 分类任务的类别数量。
        batch_size (int): 批次大小。
        b1 (float): Adam 优化器的 Beta1 超参数。
        b2 (float): Adam 优化器的 Beta2 超参数。
        n_epochs (int): 训练的 epoch 数量。
        lr (float): 学习率。
        behavioral_features (list of str): 使用的行为特征列表。
        train_folder_path (str): 包含训练数据的文件夹路径。
        test_folder_path (str): 包含测试数据的文件夹路径。
        label_file (str): 包含标签的 CSV 文件路径。
        milestones (list of int): 学习率调度器的里程碑（调整学习率的 epoch 索引）。
        gamma (float): 学习率衰减的乘数因子。
        patience (int): 早停机制的耐心值（允许的连续无改进 epoch 数量）。
        sampling_frequency (int): 信号的采样频率。
        weight_decay (float): 权重衰减（L2 正则化）。
        freq_min (float): CWT 的最小频率。
        freq_max (float): CWT 的最大频率。
        tensor_height (int): CWT 的离散频率数量。
        use_gated_fusion (bool): 是否使用门控融合机制。
        proj_dim (int): 投影维度。
        num_heads (int): 注意力头数量。

    返回:
        tuple: 包含测试标签和预测标签的元组。
    """

    # 设置设备为 GPU（如果可用），否则使用 CPU
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 初始化交叉熵损失函数和模型，并将其移动到设备上
    criterion_cls = torch.nn.CrossEntropyLoss().to(device)
    model = Decision_Fusion(n_classes, proj_dim=proj_dim, num_heads=num_heads, use_gated_fusion=use_gated_fusion)
    model = nn.DataParallel(model)  # 使用多 GPU 并行计算
    model = model.to(device)

    # 获取行为信号的长度
    first_file_path = os.path.join(train_folder_path, os.listdir(train_folder_path)[0])
    signal_length = len(pd.read_csv(first_file_path))
    # df = pd.read_csv(first_file_path, header=None, delimiter=",", engine="python", encoding="utf-8", on_bad_lines="skip")
    # signal_length = len(df)



    # 从训练和测试目录加载数据，并保留原始数据用于数据增强
    train_signal_data, train_label, test_signal_data, test_label = get_source_data(
        train_folder_path, test_folder_path, label_file, behavioral_features)
    # if np.isnan(train_signal_data).any() or np.isinf(train_signal_data).any():
    #     print("!! NaN or Inf detected in raw train_signal_data from get_source_data !!")
    original_data = train_signal_data
    original_label = train_label

    # 将数据转换为 PyTorch 张量并移动到设备上
    train_signal_data = torch.from_numpy(train_signal_data).float().to(device)
    # if torch.isnan(train_signal_data).any() or torch.isinf(train_signal_data).any():
    #     print("## NaN or Inf detected in train_signal_data after torch conversion!")
    train_label = torch.from_numpy(train_label).long().to(device)
    test_signal_data = torch.from_numpy(test_signal_data).float().to(device)
    test_label = torch.from_numpy(test_label).long().to(device)

    # 创建训练数据的 DataLoader
    dataset = torch.utils.data.TensorDataset(train_signal_data, train_label)
    dataloader = torch.utils.data.DataLoader(dataset=dataset, batch_size=batch_size, shuffle=True)

    # 设置优化器和学习率调度器
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, betas=(b1, b2), weight_decay=weight_decay)
    scheduler = MultiStepLR(optimizer, milestones=milestones, gamma=gamma)

    # 初始化早停机制
    early_stopping = EarlyStopping(patience=patience, verbose=True)

    # 记录训练集和测试集的大小以及使用的设备
    print(f'\nTrain set size: {train_label.shape[0]}')
    print(f'Test set size: {test_label.shape[0]}')
    print(f'\nTraining TCCT-Net...')
    print(f'Training on device: {device}')

    # 初始化列表以跟踪训练和测试性能
    best_acc = 0
    train_losses, train_accuracies, test_accuracies = [], [], []

    for e in range(n_epochs):
        print('\nEpoch:', e + 1)

        # 训练一个 epoch
        epoch_loss, train_acc, duration, current_lr = train_one_epoch(
            model, dataloader, optimizer, scheduler, criterion_cls, original_data, original_label,
            batch_size, signal_length, device, behavioral_features, freq_min, freq_max,
            tensor_height, sampling_frequency)

        # 在测试集上评估并记录指标
        test_acc, loss_test, y_pred = evaluate(
            model, test_signal_data, test_label, criterion_cls, freq_min, freq_max, tensor_height, sampling_frequency)

        # 监控门控权重（如果使用门控融合）
        if use_gated_fusion and hasattr(model.module, 'gated_fusion_TS'):
            ts_gate = model.module.gated_fusion_TS.gate.item()
            tf_gate = model.module.gated_fusion_TF.gate.item()
            print(f'Gate weights - TS: {ts_gate:.4f}, TF: {tf_gate:.4f}')

        log_metrics(e, epoch_loss, loss_test, train_acc, test_acc, best_acc, duration, current_lr)

        # 更新最佳准确率
        if test_acc > best_acc:
            best_acc = test_acc

        # 存储性能指标
        train_losses.append(epoch_loss)
        train_accuracies.append(train_acc)
        test_accuracies.append(test_acc)

        # 清理 GPU 内存
        torch.cuda.empty_cache()

        # 检查早停条件
        early_stopping(test_acc)
        if early_stopping.early_stop:
            print("Early stopping")
            break

    # 训练结束后保存最佳模型并绘制指标
    torch.save(model.module.state_dict(), 'model_weights.pth')
    print('The best accuracy is:', best_acc)
    plot_metrics(train_losses, train_accuracies, test_accuracies)

    return test_label, y_pred  # 返回测试标签和预测标签
