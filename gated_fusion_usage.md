# 门控融合机制使用指南

## 概述

本项目实现了一个门控融合机制（GatedFusion），用于融合混合注意力（HybridAttention）与CBAM（Convolutional Block Attention Module）。该机制通过可学习的门控权重自适应地平衡两种注意力机制的贡献。

## 核心组件

### 1. HybridAttention 类
```python
class HybridAttention(nn.Module):
    """
    混合注意力模块：结合多头交叉模态注意力和自注意力机制
    """
    def __init__(self, dim, num_heads=4):
        # 参数:
        # - dim: 特征维度
        # - num_heads: 注意力头数量
```

### 2. GatedFusion 类
```python
class GatedFusion(nn.Module):
    """
    门控融合机制：融合混合注意力与CBAM
    """
    def __init__(self, channels, dim=None, num_heads=4, ratio=8, kernel_size=7):
        # 参数:
        # - channels: 通道数（用于CBAM）
        # - dim: 特征维度（用于混合注意力）
        # - num_heads: 注意力头数量
        # - ratio: CBAM通道注意力的压缩比
        # - kernel_size: CBAM空间注意力的卷积核大小
```

### 3. 增强的 Decision_Fusion 类
```python
class Decision_Fusion(nn.Module):
    def __init__(self, n_classes, proj_dim=128, num_heads=4, use_gated_fusion=True):
        # 新增参数:
        # - use_gated_fusion: 是否使用门控融合机制
```

## 使用方法

### 1. 基本使用

#### 对于4D卷积特征图：
```python
import torch
from models.feature_fusion import GatedFusion

# 创建4D特征图 (batch_size, channels, height, width)
x = torch.randn(2, 64, 8, 10)

# 创建门控融合模块
gated_fusion = GatedFusion(channels=64, ratio=8, kernel_size=7)

# 前向传播
output = gated_fusion(x)
print(f"门控权重: {gated_fusion.gate.item():.4f}")
```

#### 对于2D特征向量：
```python
# 创建2D特征向量 (batch_size, seq_len, feature_dim)
x = torch.randn(4, 1, 128)

# 创建门控融合模块
gated_fusion = GatedFusion(channels=None, dim=128, num_heads=4)

# 前向传播
output = gated_fusion(x)
```

### 2. 在Decision_Fusion中使用

#### 启用门控融合：
```python
from models.feature_fusion import Decision_Fusion

# 创建使用门控融合的模型
model = Decision_Fusion(
    n_classes=4, 
    proj_dim=128, 
    num_heads=4, 
    use_gated_fusion=True  # 启用门控融合
)

# 模型输入
TS_input = torch.randn(2, 1, 4, 280)
TF_input = torch.randn(2, 4, 20, 280)

# 前向传播
output = model(TS_input, TF_input)
```

#### 使用原始双向注意力：
```python
# 创建使用原始注意力的模型
model_original = Decision_Fusion(
    n_classes=4, 
    proj_dim=128, 
    num_heads=4, 
    use_gated_fusion=False  # 使用原始双向注意力
)
```

### 3. 训练时的门控权重监控

```python
# 在训练循环中监控门控权重
for epoch in range(num_epochs):
    for batch_idx, (ts_data, tf_data, targets) in enumerate(train_loader):
        # 前向传播
        outputs = model(ts_data, tf_data)
        loss = criterion(outputs, targets)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # 监控门控权重
        if batch_idx % 100 == 0:
            ts_gate = model.gated_fusion_TS.gate.item()
            tf_gate = model.gated_fusion_TF.gate.item()
            print(f'Epoch {epoch}, Batch {batch_idx}: '
                  f'TS Gate={ts_gate:.4f}, TF Gate={tf_gate:.4f}')
```

## 工作原理

### 门控融合公式
```
output = gate * hybrid_attention(x) + (1 - gate) * cbam(x)
```

其中：
- `gate` 是可学习的参数，初始值为0.5
- `hybrid_attention(x)` 是混合注意力的输出
- `cbam(x)` 是CBAM的输出

### 自适应权重学习
- 门控权重在训练过程中自动学习
- 权重值接近1时，更偏向混合注意力
- 权重值接近0时，更偏向CBAM
- 权重值在0.5附近时，两种机制平衡融合

## 优势

1. **自适应融合**：门控权重自动学习最优的融合比例
2. **灵活性**：支持4D卷积特征图和2D特征向量
3. **兼容性**：可以轻松集成到现有模型中
4. **可解释性**：门控权重提供了融合策略的可视化

## 测试和验证

运行测试脚本：
```bash
python test_gated_fusion.py
```

该脚本将执行以下测试：
1. 4D输入的门控融合测试
2. 2D输入的门控融合测试
3. Decision_Fusion集成测试
4. 门控权重学习过程可视化
5. 不同注意力机制效果比较

## 注意事项

1. **内存使用**：门控融合会增加一定的计算开销
2. **超参数调优**：可能需要调整学习率以适应门控权重的学习
3. **初始化**：门控权重初始化为0.5，可根据需要调整
4. **设备兼容性**：确保所有组件在同一设备上（CPU/GPU）

## 扩展建议

1. **多层门控**：在不同层级应用门控融合
2. **动态门控**：根据输入特征动态调整门控权重
3. **注意力可视化**：添加注意力图可视化功能
4. **性能优化**：针对特定硬件优化计算效率
