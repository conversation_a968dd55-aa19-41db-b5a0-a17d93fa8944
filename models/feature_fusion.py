from models.temporal_frequency_stream import TF_ConvModule
from models.temporal_spatial_stream import TS_Stream, CBAM

import torch
from torch import nn
from torch.backends import cudnn

cudnn.benchmark = False
cudnn.deterministic = True

class MultiHeadCrossModalAttention(nn.Module):  # 👉 新增：定义多头交互注意力模块 双向交互注意力机制（TS ↔ TF）
    """
    多头交互注意力模块：通用版，支持任意 query 和 key/value 输入
    """
    def __init__(self, dim, num_heads=4):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        assert dim % num_heads == 0, "proj_dim must be divisible by num_heads"

        self.query_proj = nn.Linear(dim, dim)
        self.key_proj = nn.Linear(dim, dim)
        self.value_proj = nn.Linear(dim, dim)
        self.scale = self.head_dim ** -0.5

        self.out_proj = nn.Linear(dim, dim)  # 输出投影

    def forward(self, query_feat, key_value_feat):
        B = query_feat.size(0)
        Q = self.query_proj(query_feat).view(B, self.num_heads, self.head_dim)  # (B, H, D)
        K = self.key_proj(key_value_feat).view(B, self.num_heads, self.head_dim)
        V = self.value_proj(key_value_feat).view(B, self.num_heads, self.head_dim)

        attn_score = (Q * K).sum(dim=-1, keepdim=True) * self.scale  # (B, H, 1)
        attn = torch.softmax(attn_score, dim=1)  # (B, H, 1)
        attn_output = (attn * V).view(B, -1)  # 合并注意力头输出 (B, dim)

        return query_feat + self.out_proj(attn_output)  # 残差连接


class HybridAttention(nn.Module):
    """
    混合注意力模块：结合多头交叉模态注意力和自注意力机制
    """
    def __init__(self, dim, num_heads=4):
        super().__init__()
        self.cross_modal_attn = MultiHeadCrossModalAttention(dim, num_heads)
        self.self_attn = nn.MultiheadAttention(dim, num_heads, batch_first=True)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)

    def forward(self, x):
        # x的形状: (B, seq_len, dim)
        B, seq_len, dim = x.shape

        # 对每个序列位置应用交叉模态注意力
        cross_outputs = []
        for i in range(seq_len):
            # 提取当前位置的特征 (B, dim)
            current_feat = x[:, i, :]
            # 应用交叉模态注意力
            cross_out = self.cross_modal_attn(current_feat, current_feat)
            cross_outputs.append(cross_out.unsqueeze(1))  # (B, 1, dim)

        # 拼接所有位置的输出
        cross_out = torch.cat(cross_outputs, dim=1)  # (B, seq_len, dim)
        cross_out = self.norm1(cross_out)

        # 自注意力
        self_out, _ = self.self_attn(cross_out, cross_out, cross_out)
        self_out = self.norm2(self_out + cross_out)

        return self_out


class GatedFusion(nn.Module):
    """
    门控融合机制：融合混合注意力与CBAM
    """
    def __init__(self, channels, dim=None, num_heads=4, ratio=8, kernel_size=7):
        super().__init__()

        # 如果输入是4D张量（CBAM适用），需要先转换维度
        self.is_4d_input = dim is None

        if self.is_4d_input:
            # 对于4D输入，直接使用CBAM
            self.cbam = CBAM(channels, ratio, kernel_size)
            # 为了与混合注意力兼容，添加维度转换
            self.adaptive_pool = nn.AdaptiveAvgPool2d(1)
            self.hybrid_att = HybridAttention(channels, num_heads)
        else:
            # 对于已经是特征向量的输入
            self.cbam = None
            self.hybrid_att = HybridAttention(dim, num_heads)

        # 可学习的门控权重
        self.gate = nn.Parameter(torch.tensor(0.5))

        # 特征对齐层（如果需要）
        if not self.is_4d_input:
            self.feature_align = nn.Linear(dim, dim)

    def forward(self, x):
        if self.is_4d_input:
            # 对于4D输入（如卷积特征图）
            # CBAM分支
            cbam_out = self.cbam(x)

            # 混合注意力分支：需要将4D转换为适合注意力的格式
            b, c, h, w = x.shape
            # 全局平均池化得到每个通道的特征
            pooled_feat = self.adaptive_pool(x).view(b, c)  # (B, C)
            # 扩展维度以适应注意力机制
            hybrid_input = pooled_feat.unsqueeze(1)  # (B, 1, C)
            hybrid_out = self.hybrid_att(hybrid_input)  # (B, 1, C)
            hybrid_out = hybrid_out.squeeze(1)  # (B, C)

            # 将混合注意力输出重新广播到原始空间维度
            # 确保hybrid_out的维度正确
            if hybrid_out.numel() == b * c:
                hybrid_out = hybrid_out.view(b, c, 1, 1).expand(b, c, h, w)
            else:
                # 如果维度不匹配，使用原始输入的通道注意力权重
                hybrid_out = x * pooled_feat.view(b, c, 1, 1)

            # 门控融合
            fused = self.gate * hybrid_out + (1 - self.gate) * cbam_out

        else:
            # 对于特征向量输入
            # 混合注意力分支
            hybrid_out = self.hybrid_att(x)

            # CBAM无法直接应用于1D特征，使用特征对齐层模拟
            cbam_out = self.feature_align(x)

            # 门控融合
            fused = self.gate * hybrid_out + (1 - self.gate) * cbam_out

        return fused


class Decision_Fusion(nn.Module):
    """
    特征级融合版本的决策模块：使用双向多头交互注意力进行 TS↔TF 融合。

    参数:
        n_classes (int): 分类任务的类别数量。
    """

    def __init__(self, n_classes, proj_dim=128, num_heads=4, use_gated_fusion=True):
        super(Decision_Fusion, self).__init__()

        # 两个流的特征提取网络
        self.TS_Stream = TS_Stream()
        self.TF_ConvModule = TF_ConvModule()
        self.n_classes = n_classes
        self.proj_dim = proj_dim
        self.use_gated_fusion = use_gated_fusion

        # 使用 dummy 输入推理出特征维度
        dummy_TS = torch.randn(1, 1, 4, 280)
        dummy_TF = torch.randn(1, 4, 20, 280)
        with torch.no_grad():
            TS_feat = self.TS_Stream(dummy_TS)
            TF_feat = self.TF_ConvModule(dummy_TF)
            ts_dim = TS_feat.view(1, -1).size(1)
            tf_dim = TF_feat.view(1, -1).size(1)

        # 👉 添加投影模块
        self.TS_projector = nn.Linear(ts_dim, proj_dim)
        self.TF_projector = nn.Linear(tf_dim, proj_dim)

        if self.use_gated_fusion:
            # 👉 新增：门控融合模块替代原有的双向注意力
            self.gated_fusion_TS = GatedFusion(channels=None, dim=proj_dim, num_heads=num_heads)
            self.gated_fusion_TF = GatedFusion(channels=None, dim=proj_dim, num_heads=num_heads)

            # 👉 输出分类器（门控融合后的拼接，维度为 2 * proj_dim）
            self.fusion_head = nn.Sequential(
                nn.Linear(2 * proj_dim, 256),
                nn.ReLU(),
                nn.Dropout(0.5),
                nn.Linear(256, n_classes)
            )
        else:
            # 👉 原有的双向多头注意力模块
            self.attn_TS_to_TF = MultiHeadCrossModalAttention(proj_dim, num_heads)
            self.attn_TF_to_TS = MultiHeadCrossModalAttention(proj_dim, num_heads)

            # 👉 输出分类器（投影后的双向拼接，维度为 2 * proj_dim）
            self.fusion_head = nn.Sequential(
                nn.Linear(2 * proj_dim, 256),
                nn.ReLU(),
                nn.Dropout(0.5),
                nn.Linear(256, n_classes)
            )

    def forward(self, TS_input, TF_input):
        # 提取两个模态的中间特征
        TS_feature = self.TS_Stream(TS_input)
        TF_feature = self.TF_ConvModule(TF_input)

        # 展平特征向量
        TS_feature = TS_feature.view(TS_feature.size(0), -1)
        TF_feature = TF_feature.view(TF_feature.size(0), -1)

        # 投影到统一维度
        TS_proj = self.TS_projector(TS_feature)
        TF_proj = self.TF_projector(TF_feature)

        if self.use_gated_fusion:
            # 👉 门控融合：混合注意力与CBAM的融合
            # 为门控融合准备输入（添加序列维度）
            TS_proj_seq = TS_proj.unsqueeze(1)  # (B, 1, proj_dim)
            TF_proj_seq = TF_proj.unsqueeze(1)  # (B, 1, proj_dim)

            # 应用门控融合
            TS_fused = self.gated_fusion_TS(TS_proj_seq).squeeze(1)  # (B, proj_dim)
            TF_fused = self.gated_fusion_TF(TF_proj_seq).squeeze(1)  # (B, proj_dim)
        else:
            # 👉 原有的双向注意力交互
            TS_fused = self.attn_TS_to_TF(TS_proj, TF_proj)  # TS ← TF
            TF_fused = self.attn_TF_to_TS(TF_proj, TS_proj)  # TF ← TS

        # 👉 拼接融合结果
        fused_feature = torch.cat([TS_fused, TF_fused], dim=1)  # (B, 2 * proj_dim)

        # 保证 fused_feature 与 fusion_head 权重同设备
        fused_feature = fused_feature.to(self.fusion_head[0].weight.device)

        # 分类输出
        output = self.fusion_head(fused_feature)
        return output